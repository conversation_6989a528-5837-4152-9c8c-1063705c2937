
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {},
  assets: {
    'index.csr.html': {size: 41548, hash: 'e720fb9bae4a1c1df95a234676cf60d49db5426580bccb46de21c9440df0e180', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 21716, hash: '35e0016a1b1fc3939759a16fb4a9eda7a18deabfbb836008bcbdc9629bda5289', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-UJ27JA5M.css': {size: 298495, hash: 'KuJzfxHS510', text: () => import('./assets-chunks/styles-UJ27JA5M_css.mjs').then(m => m.default)}
  },
};
